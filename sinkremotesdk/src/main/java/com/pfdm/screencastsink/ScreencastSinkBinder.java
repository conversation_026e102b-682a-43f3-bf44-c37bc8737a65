package com.pfdm.screencastsink;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

public class ScreencastSinkBinder {
    public static final String TAG = "ScreencastSinkBinder";
    private final Context mContext;
    private IScreencastService mService;
    private final Runnable mBindRunnable = this::bindService;
    private final ScreencastSink mSink;

    public ScreencastSinkBinder(Context context, ScreencastSink sink) {
        mContext = context;
        mSink = sink;
    }

    public void bind() {
        Log.i(TAG, "create aidl client");

        bindService();
    }

    public void unbind() {
        Log.i(TAG, "destroy aidl client");

        try {
            mService.unregisterListener(mListener);
        } catch (Exception e) {
            Log.e(TAG, "unregister listener error, package:" + mContext.getPackageName(), e);
        }

        unbindService();
    }

    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mService = IScreencastService.Stub.asInterface(service);
            Log.i(TAG, "onServiceConnected");

            try {
                mService.registerListener(mListener);
            } catch (Exception e) {
                Log.e(TAG, "register listener error, package:" + mContext.getPackageName(), e);
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected");
            mService = null;

            if (mSink.mHandler.hasCallbacks(mBindRunnable)) {
                mSink.mHandler.removeCallbacks(mBindRunnable);
            }
            mSink.mHandler.post(mBindRunnable);
        }

        @Override
        public void onBindingDied(ComponentName name) {
            ServiceConnection.super.onBindingDied(name);
            Log.i(TAG, "onBindingDied");
            mService = null;

            if (mSink.mHandler.hasCallbacks(mBindRunnable)) {
                mSink.mHandler.removeCallbacks(mBindRunnable);
            }
            mSink.mHandler.post(mBindRunnable);
        }

        @Override
        public void onNullBinding(ComponentName name) {
            ServiceConnection.super.onNullBinding(name);
            Log.i(TAG, "onNullBinding");
            mService = null;

            if (mSink.mHandler.hasCallbacks(mBindRunnable)) {
                mSink.mHandler.removeCallbacks(mBindRunnable);
            }
            mSink.mHandler.post(mBindRunnable);
        }
    };

    private void bindService() {
        if (mService != null) {
            Log.w(TAG, "service is already bind");
            return;
        }

        Log.i(TAG, "bind screencast sink service");
        Intent intent = new Intent();
        intent.setAction("com.pfdm.intent.action.SCREENCAST_SINK");
        intent.setPackage("com.pfdm.screencastsink");

        try {
            mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
        } catch (Exception e) {
            Log.e(TAG, "bind service error", e);
        }
    }

    private void unbindService() {
        Log.i(TAG, "unbind screencast sink service");
        try {
            mContext.unbindService(mServiceConnection);
        } catch (Exception e) {
            Log.e(TAG, "unbind service error", e);
        }
    }

    public IScreencastService getService() {
        return mService;
    }

    private final IScreencastListener mListener = new IScreencastListener.Stub() {

        @Override
        public void onVideoSizeChanged(int id, int width, int height) throws RemoteException {
            Log.i(TAG, "on video size changed, id:" + id + " w:" + width + " h:" + height
                    + ", updated SurfaceTexture w:" + mSink.mDeviceWidth[id] + " h:" + mSink.mDeviceHeight[id]);
            mSink.mSurfaceTexture[id].setDefaultBufferSize(width, height);
        }
    };
}
