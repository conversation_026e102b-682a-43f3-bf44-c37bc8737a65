package com.pfdm.screencastsink;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.graphics.Bitmap;
import android.graphics.SurfaceTexture;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.opengl.GLES11Ext;
import android.opengl.GLES32;
import android.os.Handler;
import android.os.HandlerThread;
import android.provider.Settings;
import android.util.Log;
import android.view.PixelCopy;
import android.view.Surface;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import org.json.JSONObject;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.nio.file.Files;
import java.nio.file.Paths;


public class ScreencastSink {
    private static final String TAG = "ScreencastSink";
    public static final int DEVICE_ID_DP = 0;
    public static final int DEVICE_ID_HDMI = 1;
    public static final int DEVICE_ID_MAX = 2;

    @IntDef({DEVICE_ID_DP, DEVICE_ID_HDMI})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DeviceId {
    }

    public static final int DEVICE_STATE_NONE = 0;
    public static final int DEVICE_STATE_DP_ACTIVATED = 1;
    public static final int DEVICE_STATE_HDMI_ACTIVATED = 1 << 1;
    public static final int DEVICE_STATE_WIFI_DIRECT_CONNECTED = 1 << 2;
    public static final int DEVICE_STATE_BLE_CONNECTED = 1 << 3;

    @IntDef({DEVICE_STATE_NONE, DEVICE_STATE_DP_ACTIVATED, DEVICE_STATE_HDMI_ACTIVATED, DEVICE_STATE_WIFI_DIRECT_CONNECTED})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DeviceState {
    }

    public static final int BOX_STATE_SLEEP = 0;
    public static final int BOX_STATE_IDLE = 1;

    public static final int DEFAULT_VIDEO_DROP_THRESHOLD = 2;
    public static final int DEFAULT_AUDIO_DROP_THRESHOLD = 10;

    public static final int FLAG_STREAMING_WINDOW_INVISIBILITY = 0;
    public static final int FLAG_STREAMING_WINDOW_DP_VISIBILITY = 1;
    public static final int FLAG_STREAMING_WINDOW_HDMI_VISIBILITY = 1 << 1;
    public static final int FLAG_STREAMING_WINDOW_SETTINGS_VISIBILITY = 1 << 2;
    public static final int FLAG_STREAMING_WINDOW_FOCUSED = 1 << 3;
    public static final int FLAG_STREAMING_WINDOW_COVERED = 1 << 4;

    protected static final String NAME_DP_STREAMING_STATE = "box_dp_streaming_state";
    protected static final String NAME_HDMI_STREAMING_STATE = "box_hdmi_streaming_state";
    protected static final String NAME_STREAMING_WINDOW_STATE = "box_streaming_window_state";
    protected static final String NAME_DEVICE_DP_CONFIG = "box_dp_config";
    protected static final String NAME_DEVICE_HDMI_CONFIG = "box_hdmi_config";
    protected static final String NAME_WIFI_DIRECT_CONNECTED = "box_wd_connected";
    protected static final String NAME_WIFI_DIRECT_CONNECTED_REASON = "box_wd_connected_reason";
    protected static final String NAME_WIFI_DIRECT_WORK_FREQ = "box_wd_work_freq";
    protected static final String NAME_BLE_CONNECTED_STATE = "ble_connect_state";
    protected static final String NAME_DP_DISPLAY_TYPE = "box_dp_display_type";
    protected static final String NAME_HDMI_DISPLAY_TYPE = "box_hdmi_display_type";
    protected static final String NAME_BATTERY_INFO = "box_battery_info";
    protected static final String NAME_BOND_STATE = "box_bond_state";
    protected static final String NAME_BOX_STATE = "box_state";
    protected static final String NAME_VIDEO_DROP_THRESHOLD = "box_video_drop_threshold";
    protected static final String NAME_AUDIO_DROP_THRESHOLD = "box_audio_drop_threshold";
    protected static final String NAME_STATISTICS_LEVEL = "box_statistics_level";
    protected static final String NAME_WIFI_STATE = "box_wifi_state";
    protected static final String NAME_BUILD_INFO = "box_build_info";
    protected static final String NAME_TRACE_LEVEL = "box_trace_level";

    protected static final Uri URI_DEVICE_DP_CONFIG = Settings.System.getUriFor(NAME_DEVICE_DP_CONFIG);
    protected static final Uri URI_DEVICE_HDMI_CONFIG = Settings.System.getUriFor(NAME_DEVICE_HDMI_CONFIG);
    protected static final Uri URI_NAME_WIFI_DIRECT_CONNECTED = Settings.System.getUriFor(NAME_WIFI_DIRECT_CONNECTED);
    protected static final Uri URI_NAME_BLE_CONNECTED_STATE = Settings.System.getUriFor(NAME_BLE_CONNECTED_STATE);
    protected static final Uri URI_NAME_DP_DISPLAY_TYPE = Settings.System.getUriFor(NAME_DP_DISPLAY_TYPE);
    protected static final Uri URI_NAME_HDMI_DISPLAY_TYPE = Settings.System.getUriFor(NAME_HDMI_DISPLAY_TYPE);
    protected static final Uri URI_NAME_BATTERY_INFO = Settings.System.getUriFor(NAME_BATTERY_INFO);
    protected static final Uri URI_NAME_BOND_STATE = Settings.System.getUriFor(NAME_BOND_STATE);
    protected static final Uri URI_NAME_BOX_STATE = Settings.System.getUriFor(NAME_BOX_STATE);
    protected static final Uri URI_NAME_STREAMING_WINDOW_STATE = Settings.System.getUriFor(NAME_STREAMING_WINDOW_STATE);
    protected static final Uri URI_NAME_VIDEO_DROP_THRESHOLD = Settings.System.getUriFor(NAME_VIDEO_DROP_THRESHOLD);
    protected static final Uri URI_NAME_AUDIO_DROP_THRESHOLD = Settings.System.getUriFor(NAME_AUDIO_DROP_THRESHOLD);
    protected static final Uri URI_NAME_STATISTICS_LEVEL = Settings.System.getUriFor(NAME_STATISTICS_LEVEL);
    protected static final Uri URI_NAME_WIFI_STATE = Settings.System.getUriFor(NAME_WIFI_STATE);
    protected static final Uri URI_NAME_BUILD_INFO = Settings.System.getUriFor(NAME_BUILD_INFO);
    protected static final Uri URI_NAME_TRACE_LEVEL = Settings.System.getUriFor(NAME_TRACE_LEVEL);

    private final Context mContext;
    protected final SurfaceTexture[] mSurfaceTexture = new SurfaceTexture[DEVICE_ID_MAX];
    protected final Surface[] mSurface = new Surface[DEVICE_ID_MAX];
    protected final Device[] mDevices = new Device[DEVICE_ID_MAX];
    protected final Status mStatus = new Status();
    protected final BuildInfo mBuildInfo = new BuildInfo();
    private final Object mLock = new Object();
    protected IScreencastSinkListener mListener;
    protected Handler mFrameHandler;
    protected HandlerThread mFrameHandlerThread;
    protected Handler mHandler;
    protected HandlerThread mHandlerThread;
    private ContentObserver mContentObserver;

    public ScreencastSink(Context context) {
        mContext = context;
    }

    public void init() {
        Log.i(TAG, "init");

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("com.pfdm.action.DUMP_SINK_IMAGE");
        mContext.registerReceiver(mReceiver, intentFilter, Context.RECEIVER_EXPORTED);

        Settings.System.putInt(mContext.getContentResolver(), NAME_DP_STREAMING_STATE, 0);
        Settings.System.putInt(mContext.getContentResolver(), NAME_HDMI_STREAMING_STATE, 0);
        Settings.System.putInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);

        mDevices[DEVICE_ID_DP] = new Device();
        mDevices[DEVICE_ID_HDMI] = new Device();

        mFrameHandlerThread = new HandlerThread("screencast-frame");
        mFrameHandlerThread.setPriority(Thread.MAX_PRIORITY);
        mFrameHandlerThread.start();
        mFrameHandler = new Handler(mFrameHandlerThread.getLooper());

        mHandlerThread = new HandlerThread("screencast-sink");
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper());

        mContentObserver = new ContentObserver(mHandler) {
            @Override
            public void onChange(boolean selfChange, @Nullable Uri uri) {
                super.onChange(selfChange, uri);

                if (URI_DEVICE_DP_CONFIG.equals(uri)) {
                    String value = Settings.System.getString(mContext.getContentResolver(), NAME_DEVICE_DP_CONFIG);
                    Log.i(TAG, "device dp config changed, value:" + value);
                    updateDeviceConfig(ScreencastSink.DEVICE_ID_DP, value);
                } else if (URI_DEVICE_HDMI_CONFIG.equals(uri)) {
                    String value = Settings.System.getString(mContext.getContentResolver(), NAME_DEVICE_HDMI_CONFIG);
                    Log.i(TAG, "device hdmi config changed, value:" + value);
                    updateDeviceConfig(ScreencastSink.DEVICE_ID_HDMI, value);
                } else if (URI_NAME_WIFI_DIRECT_CONNECTED.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_CONNECTED, 0);
                    Log.i(TAG, "wifi direct state changed, value:" + value);
                    int reasonCode = -1;
                    int workFrequency = -1;
                    if (value == 1) {
                        reasonCode = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_CONNECTED_REASON, 0);
                        workFrequency = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_WORK_FREQ, 0);
                    }
                    updateDeviceWifiDirectConnectionState(value, reasonCode, workFrequency);
                    toggleStreaming();
                } else if (URI_NAME_BLE_CONNECTED_STATE.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_BLE_CONNECTED_STATE, 0);
                    Log.i(TAG, "ble state changed, value:" + value);
                    updateDeviceBleConnectionState(value);
                    toggleStreaming();
                } else if (URI_NAME_DP_DISPLAY_TYPE.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_DP_DISPLAY_TYPE, 0);
                    Log.i(TAG, "dp display type changed, value:" + value);
                    updateDisplayType(DEVICE_ID_DP, value);
                } else if (URI_NAME_HDMI_DISPLAY_TYPE.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_HDMI_DISPLAY_TYPE, 0);
                    Log.i(TAG, "hdmi display type changed, value:" + value);
                    updateDisplayType(DEVICE_ID_HDMI, value);
                } else if (URI_NAME_BATTERY_INFO.equals(uri)) {
                    String value = Settings.System.getString(mContext.getContentResolver(), NAME_BATTERY_INFO);
                    Log.i(TAG, "battery info changed, value:" + value);
                    updateBatteryInfo(value);
                } else if (URI_NAME_BOND_STATE.equals(uri)) {
                    String value = Settings.System.getString(mContext.getContentResolver(), NAME_BOND_STATE);
                    Log.i(TAG, "on bond state changed, value:" + value);
                    updateBondState(value);
                } else if (URI_NAME_BOX_STATE.equals(uri)) {
                    int boxState = Settings.System.getInt(mContext.getContentResolver(), NAME_BOX_STATE, BOX_STATE_IDLE);
                    int windowState = Settings.System.getInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
                    Log.i(TAG, "on box state changed, value:" + boxState + " last value:" + mStatus.lastBoxState + " window state:" + windowState);
                    if (boxState <= BOX_STATE_IDLE && boxState < mStatus.lastBoxState && (windowState & FLAG_STREAMING_WINDOW_FOCUSED) == 0) {
                        stop(DEVICE_ID_DP);
                        stop(DEVICE_ID_HDMI);
                    }
                    mStatus.lastBoxState = boxState;
                } else if (URI_NAME_STREAMING_WINDOW_STATE.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
                    Log.i(TAG, "on streaming window state changed, value:" + Integer.toBinaryString(value));

                    ScreencastSinkNative.setFocused((value & FLAG_STREAMING_WINDOW_FOCUSED) != 0);
                    toggleStreaming();
                } else if (URI_NAME_VIDEO_DROP_THRESHOLD.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_VIDEO_DROP_THRESHOLD, DEFAULT_VIDEO_DROP_THRESHOLD);
                    Log.i(TAG, "on video frame drop threshold changed, value:" + value);

                    updateVideoFrameDropThreshold(value);
                    ScreencastSinkNative.setVideoFrameDropThreshold(DEVICE_ID_DP, value);
                    ScreencastSinkNative.setVideoFrameDropThreshold(DEVICE_ID_HDMI, value);
                } else if (URI_NAME_AUDIO_DROP_THRESHOLD.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_AUDIO_DROP_THRESHOLD, DEFAULT_AUDIO_DROP_THRESHOLD);
                    Log.i(TAG, "on audio frame drop threshold changed, value:" + value);

                    updateAudioFrameDropThreshold(value);
                    ScreencastSinkNative.setAudioFrameDropThreshold(DEVICE_ID_DP, value);
                    ScreencastSinkNative.setAudioFrameDropThreshold(DEVICE_ID_HDMI, value);
                } else if (URI_NAME_STATISTICS_LEVEL.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_STATISTICS_LEVEL, 0);
                    Log.i(TAG, "on statistics level changed, value:" + value);
                    updateStatisticsLevel(value);
                    ScreencastSinkNative.setStatisticsLevel(value);
                } else if (URI_NAME_WIFI_STATE.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_STATE, WifiManager.WIFI_STATE_DISABLED);
                    Log.i(TAG, "on wifi state changed, value:" + value);
                    updateWifiState(value);
                } else if (URI_NAME_BUILD_INFO.equals(uri)) {
                    String value = Settings.System.getString(mContext.getContentResolver(), NAME_BUILD_INFO);
                    Log.i(TAG, "on build info changed, value:" + value);
                    updateBuildInfo(value);
                } else if (URI_NAME_TRACE_LEVEL.equals(uri)) {
                    int value = Settings.System.getInt(mContext.getContentResolver(), NAME_TRACE_LEVEL, 0);
                    Log.i(TAG, "on trace level changed, value:" + value);
                    updateTraceLevel(value);
                    ScreencastSinkNative.setTraceLevel(value);
                } else {
                    Log.w(TAG, "invalid uri:" + uri);
                }
            }
        };

        mContext.getContentResolver().registerContentObserver(URI_DEVICE_DP_CONFIG, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_DEVICE_HDMI_CONFIG, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_WIFI_DIRECT_CONNECTED, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_BLE_CONNECTED_STATE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_DP_DISPLAY_TYPE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_HDMI_DISPLAY_TYPE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_BATTERY_INFO, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_BOND_STATE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_BOX_STATE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_STREAMING_WINDOW_STATE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_VIDEO_DROP_THRESHOLD, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_AUDIO_DROP_THRESHOLD, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_STATISTICS_LEVEL, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_WIFI_STATE, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_BUILD_INFO, true, mContentObserver);
        mContext.getContentResolver().registerContentObserver(URI_NAME_TRACE_LEVEL, true, mContentObserver);

        String dpValue = Settings.System.getString(mContext.getContentResolver(), NAME_DEVICE_DP_CONFIG);
        Log.i(TAG, "init dp value:" + dpValue);
        updateDeviceConfig(DEVICE_ID_DP, dpValue);

        String hdmiValue = Settings.System.getString(mContext.getContentResolver(), NAME_DEVICE_HDMI_CONFIG);
        Log.i(TAG, "init hdmi value:" + hdmiValue);
        updateDeviceConfig(DEVICE_ID_HDMI, hdmiValue);

        int value = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_CONNECTED, 0);
        Log.i(TAG, "init wifi direct value:" + value);
        int reasonCode = -1;
        int workFrequency = -1;
        if (value == 1) {
            reasonCode = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_CONNECTED_REASON, 0);
            workFrequency = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_WORK_FREQ, 0);
        }

        updateDeviceWifiDirectConnectionState(value, reasonCode, workFrequency);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_BLE_CONNECTED_STATE, 0);
        Log.i(TAG, "init ble value:" + value);
        updateDeviceBleConnectionState(value);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_DP_DISPLAY_TYPE, 1);
        Log.i(TAG, "init dp display type value:" + value);
        updateDisplayType(DEVICE_ID_DP, value);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_HDMI_DISPLAY_TYPE, 1);
        Log.i(TAG, "init hdmi display type value:" + value);
        updateDisplayType(DEVICE_ID_HDMI, value);

        String batteryValue = Settings.System.getString(mContext.getContentResolver(), NAME_BATTERY_INFO);
        Log.i(TAG, "init battery info, value:" + batteryValue);
        updateBatteryInfo(batteryValue);

        String bondSn = Settings.System.getString(mContext.getContentResolver(), NAME_BOND_STATE);
        Log.i(TAG, "init bond sn:" + bondSn);
        updateBondState(bondSn);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_VIDEO_DROP_THRESHOLD, DEFAULT_VIDEO_DROP_THRESHOLD);
        Log.i(TAG, "init video frame drop threshold:" + value);
        updateVideoFrameDropThreshold(value);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_AUDIO_DROP_THRESHOLD, DEFAULT_AUDIO_DROP_THRESHOLD);
        Log.i(TAG, "init audio frame drop threshold:" + value);
        updateAudioFrameDropThreshold(value);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_STATISTICS_LEVEL, 0);
        Log.i(TAG, "init statistics level:" + value);
        updateStatisticsLevel(value);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_STATE, WifiManager.WIFI_STATE_DISABLED);
        Log.i(TAG, "init wifi state:" + value);
        updateWifiState(value);

        String buildInfo = Settings.System.getString(mContext.getContentResolver(), NAME_BUILD_INFO);
        Log.i(TAG, "init build info:" + buildInfo);
        updateBuildInfo(buildInfo);

        value = Settings.System.getInt(mContext.getContentResolver(), NAME_TRACE_LEVEL, 0);
        Log.i(TAG, "init trace level:" + value);
        updateTraceLevel(value);

        ScreencastSinkNative.init();
    }

    public void clear() {
        Log.i(TAG, "clear");
        mContext.unregisterReceiver(mReceiver);

        for (SurfaceTexture surfaceTexture : mSurfaceTexture) {
            if (surfaceTexture != null) {
                surfaceTexture.release();
            }
        }

        for (Surface surface : mSurface) {
            if (surface != null) {
                surface.release();
            }
        }

        mContext.getContentResolver().unregisterContentObserver(mContentObserver);

        mHandler.removeCallbacksAndMessages(null);
        mHandlerThread.quitSafely();

        mFrameHandler.removeCallbacksAndMessages(null);
        mFrameHandlerThread.quitSafely();

        ScreencastSinkNative.clear();
    }

    private void updateDeviceConfig(@DeviceId int id, String value) {
        int w = 0, h = 0, fps = 0, state = 0, fs = 0;

        try {
            JSONObject jsonObject = new JSONObject(value);
            w = jsonObject.getInt("w");
            h = jsonObject.getInt("h");
            fps = jsonObject.getInt("fps");
            state = jsonObject.getInt("state");
            fs = jsonObject.getInt("fs");
        } catch (Exception e) {
            Log.i(TAG, "get device config error", e);
        }

        if (mDevices[id].width != w || mDevices[id].height != h) {
            mDevices[id].width = w;
            mDevices[id].height = h;

            if (mListener != null) {
                mListener.onDeviceResolutionChanged(id, w, h);
            }
        }

        if (mDevices[id].fps != fps) {
            mDevices[id].fps = fps;
        }

        if (mDevices[id].sampleRate != fs) {
            mDevices[id].sampleRate = fs;
        }

        int deviceState = id == ScreencastSink.DEVICE_ID_DP ? DEVICE_STATE_DP_ACTIVATED : DEVICE_STATE_HDMI_ACTIVATED;
        int bitValue = (mStatus.state & deviceState) != 0 ? 1 : 0;
        if (bitValue != state) {
            if (state == 1) {
                addDeviceState(deviceState);
            } else {
                removeDeviceState(deviceState);
            }

            if (mListener != null) {
                mListener.onDeviceStateChanged(mStatus.state, -1, -1);
            }
        }
    }

    private void updateDeviceWifiDirectConnectionState(int value, int p2pConnectReasonCode, int p2pWorkFrequency) {
        int bitValue = (mStatus.state & DEVICE_STATE_WIFI_DIRECT_CONNECTED) != 0 ? 1 : 0;
        if (value == bitValue) {
            return;
        }

        if (value == 1) {
            addDeviceState(DEVICE_STATE_WIFI_DIRECT_CONNECTED);
        } else {
            removeDeviceState(DEVICE_STATE_WIFI_DIRECT_CONNECTED);
        }

        if (mListener != null) {
            mListener.onDeviceStateChanged(mStatus.state, p2pConnectReasonCode, p2pWorkFrequency);
        }
    }

    private void updateDeviceBleConnectionState(int value) {
        int bitValue = (mStatus.state & DEVICE_STATE_BLE_CONNECTED) != 0 ? 1 : 0;
        if (value == bitValue) {
            return;
        }

        if (value == 1) {
            addDeviceState(DEVICE_STATE_BLE_CONNECTED);
        } else {
            removeDeviceState(DEVICE_STATE_BLE_CONNECTED);
        }

        if (mListener != null) {
            mListener.onDeviceStateChanged(mStatus.state, -1, -1);
        }
    }

    private void updateDisplayType(@DeviceId int id, int value) {
        if (mDevices[id].display == value) {
            return;
        }

        mDevices[id].display = value;

        if (mListener != null) {
            mListener.onDisplayTypeChanged(id, value);
        }
    }

    private void updateBatteryInfo(String value) {
        boolean isCharging = false;
        int percent = 0;

        try {
            JSONObject jsonObject = new JSONObject(value);
            isCharging = jsonObject.getInt("isCharging") == 1;
            percent = jsonObject.getInt("percent");
        } catch (Exception e) {
            Log.i(TAG, "get device battery info error", e);
        }

        if (mStatus.isCharging == isCharging && mStatus.batteryPercent == percent) {
            return;
        }

        mStatus.isCharging = isCharging;
        mStatus.batteryPercent = percent;

        if (mListener != null) {
            mListener.onBatteryInfoChanged(isCharging, percent);
        }
    }

    private void updateBondState(String sn) {
        mStatus.bondSn = sn;

        if (mListener != null) {
            mListener.onBondStateChanged(sn);
        }
    }

    private void updateVideoFrameDropThreshold(int threshold) {
        mDevices[DEVICE_ID_DP].videoThreshold = threshold;
        mDevices[DEVICE_ID_HDMI].videoThreshold = threshold;
    }

    private void updateAudioFrameDropThreshold(int threshold) {
        mDevices[DEVICE_ID_DP].audioThreshold = threshold;
        mDevices[DEVICE_ID_HDMI].audioThreshold = threshold;
    }

    private void updateStatisticsLevel(int level) {
        mStatus.statisticsLevel = level;
    }

    private void updateWifiState(int state) {
        mStatus.wifiState = state;
    }

    private void updateBuildInfo(String value) {
        String version = "";
        long time = 0;
        try {
            JSONObject jsonObject = new JSONObject(value);
            version = jsonObject.getString("version");
            time = jsonObject.getLong("time");
        } catch (Exception e) {
            Log.i(TAG, "get build info error", e);
        }

        mBuildInfo.version = version;
        mBuildInfo.time = time;
    }

    private void updateTraceLevel(int level) {
        mStatus.traceLevel = level;
    }

    private void addDeviceState(@DeviceState int state) {
        synchronized (mLock) {
            mStatus.state |= state;
        }
    }

    private void removeDeviceState(@DeviceState int state) {
        synchronized (mLock) {
            mStatus.state &= ~state;
        }
    }

    private boolean hasDeviceState(@DeviceState int state) {
        synchronized (mLock) {
            return (mStatus.state & state) != 0;
        }
    }

    private void toggleStreaming() {
        if (hasWindowFlag(FLAG_STREAMING_WINDOW_DP_VISIBILITY) && isBleConnected() && isDeviceConnected()) {
            if (hasWindowFlag(FLAG_STREAMING_WINDOW_FOCUSED)) start(DEVICE_ID_DP);
        } else {
            stop(DEVICE_ID_DP);
        }

        if (hasWindowFlag(FLAG_STREAMING_WINDOW_HDMI_VISIBILITY) && isBleConnected() && isDeviceConnected()) {
            if (hasWindowFlag(FLAG_STREAMING_WINDOW_FOCUSED)) start(DEVICE_ID_HDMI);
        } else {
            stop(DEVICE_ID_HDMI);
        }
    }

    public boolean setTexture(int id, int texture) {
        Log.i(TAG, String.format("id:%d setTexture %d", id, texture));
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);

        mSurfaceTexture[value] = new SurfaceTexture(texture);
        mSurface[value] = new Surface(mSurfaceTexture[value]);
        mSurfaceTexture[value].setOnFrameAvailableListener(surfaceTexture -> {
            ScreencastSinkNative.onFrameAvailable(value);

            if (mListener != null) {
                mListener.onFrameAvailable(value);
            }
        }, mFrameHandler);

        ScreencastSinkNative.setSurfaceTexture(value, mSurfaceTexture[value]);
        return true;
    }

    public void start(int id) {
        Log.i(TAG, String.format("id:%d start", id));
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);

        if (ScreencastSinkNative.isRunning(value)) {
            Log.i(TAG, "screencast sink has started");
            return;
        }

        ScreencastSinkNative.setSourceBuildInfo(mBuildInfo.version, mBuildInfo.time);
        ScreencastSinkNative.setStatisticsLevel(mStatus.statisticsLevel);
        ScreencastSinkNative.setTraceLevel(mStatus.traceLevel);
        ScreencastSinkNative.setIp(value, "************");
        ScreencastSinkNative.setVideoFrameDropThreshold(value, mDevices[value].videoThreshold);
        ScreencastSinkNative.setAudioFrameDropThreshold(value, mDevices[value].audioThreshold);
        ScreencastSinkNative.setCallback(mSinkCallback);
        ScreencastSinkNative.start(value);
    }

    public void stop(int id) {
        Log.i(TAG, String.format("id:%d stop", id));
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);

        if (!ScreencastSinkNative.isRunning(value)) {
            Log.i(TAG, "screencast sink has stopped");
            return;
        }

        ScreencastSinkNative.stop(value);
    }

    public boolean isRunning(int id) {
        Log.i(TAG, String.format("id:%d isRunning", id));
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);

        return ScreencastSinkNative.isRunning(value);
    }

    private final ScreencastSinkNative.Callback mSinkCallback = new ScreencastSinkNative.Callback() {
        @Override
        public void onStateChanged(int id, int state) {
            if (id == DEVICE_ID_DP) {
                Settings.System.putInt(mContext.getContentResolver(), NAME_DP_STREAMING_STATE, state);
            } else if (id == DEVICE_ID_HDMI) {
                Settings.System.putInt(mContext.getContentResolver(), NAME_HDMI_STREAMING_STATE, state);
            } else {
                Log.i(TAG, "invalid id:" + id);
            }
        }

        @Override
        public void onVideoSizeChanged(int id, int width, int height) {
            Log.i(TAG, "on video size changed, id:" + id + " w:" + width + " h:" + height
                    + ", updated SurfaceTexture w:" + mDevices[id].width + " h:" + mDevices[id].height);
            mSurfaceTexture[id].setDefaultBufferSize(mDevices[id].width, mDevices[id].height);
        }
    };

    public void addWindowFlag(int flag) {
        synchronized (mLock) {
            Log.i(TAG, "add window flag:" + flag);
            int value = Settings.System.getInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
            value |= flag;

            Settings.System.putInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, value);
        }
    }

    public void removeWindowFlag(int flag) {
        synchronized (mLock) {
            Log.i(TAG, "remove window flag:" + flag);
            int value = Settings.System.getInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
            value &= ~flag;

            Settings.System.putInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, value);
        }
    }

    public boolean hasWindowFlag(int flag) {
        synchronized (mLock) {
            int value = Settings.System.getInt(mContext.getContentResolver(), NAME_STREAMING_WINDOW_STATE, 0);
            return (value & flag) != 0;
        }
    }

    public int createTexture() {
        int[] textures = new int[1];
        GLES32.glGenTextures(1, textures, 0);
        GLES32.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, textures[0]);
        Log.i(TAG, "create texture " + textures[0]);

        return textures[0];
    }

    public void deleteTexture(int texture) {
        Log.i(TAG, "delete texture " + texture);
        GLES32.glDeleteTextures(1, new int[]{texture}, 0);
    }

    public void updateTexImage(int id) {
        ScreencastSinkNative.clearAvailable(id);
    }

    public int getDeviceWidth(int id) {
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);
        return mDevices[value].width;
    }

    public int getDeviceHeight(int id) {
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);
        return mDevices[value].height;
    }

    public boolean isDeviceActivated(int id) {
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);
        if (value == DEVICE_ID_DP) {
            return hasDeviceState(DEVICE_STATE_DP_ACTIVATED);
        } else if (value == DEVICE_ID_HDMI) {
            return hasDeviceState(DEVICE_STATE_HDMI_ACTIVATED);
        }

        return false;
    }

    public boolean isDeviceConnected() {
        return hasDeviceState(DEVICE_STATE_WIFI_DIRECT_CONNECTED);
    }

    /**
     *  获取P2P连接原因
     *  0  p2p disconnect
     *  1  p2p connect by normal
     *  2  p2p connect by reset
     * */
    public int getDeviceP2pConnectionReason () {
        return Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_CONNECTED_REASON, 0);
    }

    /**
     *  获取P2P工作具体频段
     * */
    public int getDeviceP2pWorkFreq() {
        return Settings.System.getInt(mContext.getContentResolver(), NAME_WIFI_DIRECT_WORK_FREQ, 0);
    }

    public boolean isBleConnected() {
        return hasDeviceState(DEVICE_STATE_BLE_CONNECTED);
    }

    public int getDeviceState() {
        return mStatus.state;
    }

    public int getDisplayType(int id) {
        final int value = clamp(id, DEVICE_ID_DP, DEVICE_ID_HDMI);
        return mDevices[id].display;
    }

    public boolean isDeviceBatteryCharging() {
        return mStatus.isCharging;
    }

    public int getDeviceBatteryPercent() {
        return mStatus.batteryPercent;
    }

    public String getBondSn() {
        return mStatus.bondSn;
    }

    public boolean isWifiEnabled() {
        return mStatus.wifiState == WifiManager.WIFI_STATE_ENABLED;
    }

    public void setListener(IScreencastSinkListener listener) {
        mListener = listener;
    }

    public int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }

    protected static class Device {
        int width;
        int height;
        int fps;
        int sampleRate;
        int display;
        int videoThreshold;
        int audioThreshold;

        @NonNull
        @Override
        public String toString() {
            return "width:" + width
                    + " height:" + height
                    + " fps:" + fps
                    + " sampleRate:" + sampleRate
                    + " display:" + display
                    + " videoThreshold:" + videoThreshold
                    + " audioThreshold:" + audioThreshold;
        }
    }

    protected static class Status {
        String bondSn;
        boolean isCharging;
        int state;
        int batteryPercent;
        int lastBoxState;
        int wifiState;
        int statisticsLevel;
        int traceLevel;

        @NonNull
        @Override
        public String toString() {
            return "bondSn:" + bondSn
                    + " isCharging:" + isCharging
                    + " state:" + state
                    + " batteryPercent:" + batteryPercent
                    + " lastBoxState:" + lastBoxState
                    + " wifiState:" + wifiState
                    + " statisticsLevel:" + statisticsLevel
                    + " traceLevel:" + traceLevel;
        }
    }

    protected static class BuildInfo {
        String version;
        long time;

        @NonNull
        @Override
        public String toString() {
            return "version:" + version + " time:" + time;
        }
    }

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) return;
            String action = intent.getAction();
            if ("com.pfdm.action.DUMP_SINK_IMAGE".equals(action)) {
                final int value = clamp(intent.getIntExtra("id", DEVICE_ID_DP), DEVICE_ID_DP, DEVICE_ID_HDMI);

                if (mSurface[value] == null) {
                    Log.w(TAG, "surface is null");
                    return;
                }

                if (mDevices[value].width == 0 || mDevices[value].height == 0) {
                    Log.w(TAG, "width and height must be > 0, w:" + mDevices[value].width + " h:" + mDevices[value].height);
                    return;
                }

                Bitmap bitmap = Bitmap.createBitmap(mDevices[value].width, mDevices[value].height, Bitmap.Config.ARGB_8888);
                PixelCopy.request(mSurface[value], bitmap, copyResult -> {
                    if (copyResult != PixelCopy.SUCCESS) {
                        Log.e(TAG, "screenshot error code:" + copyResult);
                        return;
                    }

                    String file = "sdcard/" + System.currentTimeMillis() + ".jpg";
                    BufferedOutputStream bos = null;
                    try {
                        bos = new BufferedOutputStream(Files.newOutputStream(Paths.get(file)));
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
                        bitmap.recycle();
                        bos.flush();
                        bos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }, mFrameHandler);
            }
        }
    };
}
