package com.pfdm.screencastsink;

public interface IScreencastSinkListener {
    void onDeviceStateChanged(int state, int p2pReasonCode, int p2pWorkFreq);

    void onDeviceResolutionChanged(int id, int width, int height);

    void onDisplayTypeChanged(int id, int type);

    void onBatteryInfoChanged(boolean isCharging, int percent);

    void onFrameAvailable(int id);

    void onBondStateChanged(String sn);
}
