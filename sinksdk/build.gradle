plugins {
    alias(libs.plugins.android.library)
}

android {
    namespace 'com.pfdm.screencastsink.sinksdk'
    compileSdk 34

    defaultConfig {
        minSdk 29

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    android.libraryVariants.configureEach { variant ->
        variant.outputs.all {
            if (outputFileName.endsWith('.aar')) {
                outputFileName = "screencastsinksdk-${variant.name}.aar"
            }
        }
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation project(':sinknative')
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}