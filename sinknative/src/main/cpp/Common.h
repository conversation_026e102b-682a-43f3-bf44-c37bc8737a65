#ifndef SCREENCASTSINK_COMMON_H
#define SCREENCASTSINK_COMMON_H

#include <android/log.h>
#include <jni.h>

#define LOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, "ScreencastSinkNative", __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, "ScreencastSinkNative", __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, "ScreencastSinkNative", __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, "ScreencastSinkNative", __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, "ScreencastSinkNative", __VA_ARGS__)


typedef enum
{
    MEDIACODEC_AUDIO_UNKNOWN = 0,  // unknow
    MEDIACODEC_AUDIO_AAC = 1,      // AAC
    MEDIACODEC_AUDIO_OPUS = 2,
    MEDIACODEC_AUDIO_PCM = 3,
    MEDIACODEC_AUDIO_PCMA = 4,
    MEDIACODEC_AUDIO_PCMU = 5,
    MEDIACODEC_AUDIO_FLAC = 6,
} DeviceAudioCodec;

typedef enum {
    DEVICE_ID_DP = 0,
    DEVICE_ID_HDMI,
    DEVICE_ID_MAX
} DeviceID;

typedef struct {
    JNIEnv *env;
    JavaVM *jvm;
    jclass clazz;
} AppContext;

typedef enum {
    AUDIO_CODEC_UNKNOWN = 0,
    AUDIO_CODEC_AAC,
    AUDIO_CODEC_PCM
} AudioCodec;

typedef enum {
    VIDEO_CODEC_UNKNOWN = 0,
    VIDEO_CODEC_H264,
    VIDEO_CODEC_H265
} VideoCodec;

#pragma pack(push, 1)
typedef struct YvrnetAudioStatus {
    int ID;
    int streamID;
    int channel;
    int samplerate;
    int profile;
    int format;
    uint64_t u64CapTime;
    uint64_t u64EncTime;
    uint64_t u64SendTime;
} YvrnetAudioStatus;

typedef struct YvrnetStatus {
    int capID;
    int sendID;
    int streamID;
    uint64_t u64FrameBeginTime;
    uint64_t u64CapTime;
    uint64_t u64EncTime;
    uint64_t u64SendTime;
    uint64_t u64DecTime;
    uint64_t u64InterTime;
    int dwFrameLen;
    bool bVideoFrame;
    int reserved1;
    int reserved2;
} YvrnetStatus;

typedef struct YvrStatistic {
    int streamID;
    int dwSendFps;
    float fbitrate;
    float fRenderAvg;
    float fnetAvg;
    float fencAvg;
    int dwencFps;
    float fdecAvg;
    float ftotalAvg;
} YvrStatistic;
#pragma pack(pop)

class JNIThreadAttacher {
public:
    JNIThreadAttacher(JavaVM *jvm) {
        // vm_ = AndroidRuntime::getJavaVM();
        jvm_ = jvm;
        status_ = jvm_->GetEnv((void **) &env_, JNI_VERSION_1_6);

        if (status_ != JNI_OK && status_ != JNI_EDETACHED) {
            LOGE("JNIThreadAttacher: unable to get environment for JNI CALL, status: %d", status_);
            env_ = nullptr;
            return;
        }

        if (status_ == JNI_EDETACHED) {
            if (jvm_->AttachCurrentThread(&env_, 0) != 0) {
                LOGE("JNIThreadAttacher: unable to attach thread to VM");
                env_ = nullptr;
                return;
            }
        }
    }

    ~JNIThreadAttacher() {
        if (status_ == JNI_EDETACHED)
            jvm_->DetachCurrentThread();
    }

    JNIEnv *getEnv() { return env_; }

private:
    JavaVM *jvm_;
    JNIEnv *env_;
    jint status_;
};

inline uint64_t getTimestampUs(){
#ifdef WIN32
    auto duration = std::chrono::system_clock::now().time_since_epoch();
	uint64_t Current = std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
#else
    timeval tv;
    gettimeofday(&tv, NULL);

    uint64_t Current = (uint64_t)tv.tv_sec * 1000 * 1000 + tv.tv_usec;
#endif
    return Current;
}

inline long getCurrentTimestampUs() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch());
    return static_cast<long>(duration.count());
}

#endif //SCREENCASTSINK_COMMON_H
