#include "Streamer.h"

Streamer::Streamer() {
    mRunning = false;
    mConnected = false;
    mFocused = true;
    mMediaPlayer = nullptr;
    mCallback = nullptr;
}

Streamer::~Streamer() = default;

void Streamer::configure(Streamer::Config &config) {
    mConfig = config;
}

void Streamer::start() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (mRunning) {
        LOGW("streamer has been started");
        return;
    }
    mRunning = true;
    LOGI("[%d]streamer start", mConfig.id);

    mMediaPlayer = std::make_shared<MediaPlayer>();
    mMediaPlayer->setSurfaceTexture(mConfig.surfaceTexture);
    mMediaPlayer->setWindow(mConfig.window);
    mMediaPlayer->configure(mConfig.player);
    mMediaPlayer->setCallback(this);

    mMediaPlayer->start();
    mMediaPlayer->setFocused(mFocused);
    LOGI("[%d]streamer started", mConfig.id);
}

void Streamer::stop() {
    std::lock_guard<std::mutex> lock(mMutex);
    if (!mRunning) {
        LOGW("[%d]streamer has been stopped", mConfig.id);
        return;
    }
    mRunning = false;
    LOGI("[%d]streamer stop", mConfig.id);

    mMediaPlayer->stop();
    mConnected = false;
    LOGI("[%d]streamer stopped", mConfig.id);
}

bool Streamer::isRunning() const {
    std::lock_guard<std::mutex> lock(mMutex);
    return mRunning;
}

bool Streamer::isConnected() const {
    return mMediaPlayer != nullptr && (mMediaPlayer->isVideoConnected() || mMediaPlayer->isAudioConnected());
}

void Streamer::setVideoFrameDropThreshold(int threshold) {
    if (mMediaPlayer != nullptr) {
        mMediaPlayer->setVideoFrameDropThreshold(threshold);
    }
}

void Streamer::setAudioFrameDropThreshold(int threshold) {
    if (mMediaPlayer != nullptr) {
        mMediaPlayer->setAudioFrameDropThreshold(threshold);
    }
}

void Streamer::setFocused(bool focused) {
    std::lock_guard<std::mutex> lock(mMutex);
    mFocused = focused;
    if (mMediaPlayer != nullptr) {
        mMediaPlayer->setFocused(focused);
    }

    LOGI("[%d]set streamer focused:%d", mConfig.id, static_cast<int>(focused));
}

void Streamer::onFrameAvailable() {
    if (mMediaPlayer != nullptr) mMediaPlayer->onFrameAvailable();
}

void Streamer::clearAvailable() {
    if (mMediaPlayer != nullptr) mMediaPlayer->clearAvailable();
}

void Streamer::setCallback(Streamer::Callback *callback) {
    mCallback = callback;
}

void Streamer::onVideoOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mCallback != nullptr) {
        int width, height;
        AMediaFormat_getInt32(mediaFormat, AMEDIAFORMAT_KEY_WIDTH, &width);
        AMediaFormat_getInt32(mediaFormat, AMEDIAFORMAT_KEY_HEIGHT, &height);

        mCallback->onVideoSizeChanged(width, height);
    }
}

void Streamer::onVideoConnectionChanged(bool connected) {
    LOGI("[%d]on video connection changed, value:%d", mConfig.id, connected);
    bool conn = Streamer::isConnected();
    if (mConnected == conn) {
        return;
    }

    mConnected = conn;
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(conn);
    }
}

void Streamer::onAudioOutputFormatChanged(AMediaFormat *mediaFormat) {
}

void Streamer::onAudioConnectionChanged(bool connected) {
    LOGI("[%d]on audio connection changed, value:%d", mConfig.id, connected);
    bool conn = Streamer::isConnected();
    if (mConnected == conn) {
        return;
    }

    mConnected = conn;
    if (mCallback != nullptr) {
        mCallback->onConnectionChanged(conn);
    }
}
