#ifndef YVRNET_YVRNET_C_WRAPPER_H
#define YVRNET_YVRNET_C_WRAPPER_H

#include "yvrnet_common.h"

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

// SDK 回调
typedef struct YvrnetCallback
{
    void (*onError)(YvrnetErrorCode err, const char* message, void *opaque);
    void (*onEvent)(const YvrnetEvent *event, void *opaque);
    void (*onStart)(YvrnetNetworkRole role, bool success, void *opaque);
    void (*onStop)(YvrnetNetworkRole role, void *opaque);
    void (*onConnectStatus)(YvrnetConnectionStatus status, void *opaque);
    void (*onStatistics)(const YvrnetStatistics *statistics, void *opaque);
    void (*onReceiveMessage)(const char *message, int length, void *opaque);
    void (*onReceivePacket)(const YvrnetMediaPacket *packet, int streamIndex, void *opaque);
} YvrnetCallback;

YVRNET_API bool yvrnet_broadcast(int port, const char* message, int length);

YVRNET_API bool yvrnet_reciverBroadcast(int port, ReceiverUdp receiver);
YVRNET_API void yvrnet_stopReciverBroadcast();

YVRNET_API bool yvrnet_startTcpClient(int port, const char* address, ReceiverTcp receiver, ConnectGuard connectGuard);
YVRNET_API bool yvrnet_sendTcpClient(const char* message, int length);
YVRNET_API void yvrnet_stopTcpClient();

YVRNET_API bool yvrnet_startTcpServer(int port, ReceiverTcp receiver, ConnectGuard connectGuard);
YVRNET_API bool yvrnet_sendTcpServer(const char* message, int length);
YVRNET_API void yvrnet_stopTcpTcpServer();

/**
 * 设置回调
 * 
 * @param instance YVRNET实例，通过 yvrnet_startup 获取
 * @param callback 回调函数
 * @param opaque 用户数据，在 YvrnetCallback 中传递
 */
YVRNET_API void yvrnet_setCallback(YvrnetInstance instance, YvrnetCallback *callback, void *opaque);

/**
 * 启动
 * 启动结果会通过 YvrnetCallback::onStart 通知
 *
 * @param instance YVRNET实例，通过 yvrnet_startup 获取
 * @param address
 *  - 服务端：监听网卡
 *  - 客户端：目标地址
 * @param port
 *  - 服务端：监听端口，如果为0，监听任意端口
 *  - 客户端：目标端口
 * @param bindPort
 *  - 服务端：返回监听的端口
 *  - 客户端：忽略该变量
 * 
 * @return YVRNET_EC_OK 成功
 * @return 其它 失败
 */
YVRNET_API YvrnetErrorCode yvrnet_start(YvrnetInstance instance, const char *address, int port, int *bindPort = NULL);

/**
 * 关闭
 * 关闭结果会通过 YvrnetCallback::onStop 通知
 * 
 * @param instance YVRNET实例，通过 yvrnet_startup 获取
 */
YVRNET_API void yvrnet_stop(YvrnetInstance instance);

/**
 * 发送消息
 *
 * @param instance YVRNET实例，通过 yvrnet_startup 获取
 * @param message 消息内容
 * @param length 消息长度（字节）
 * @param reliable 是否可靠，如果为false，则有可能丢失
 * 
 * @return 成功发送的消息长度，0 代表发送失败
 */
YVRNET_API int yvrnet_sendMessage(YvrnetInstance instance, const char *message, int length, bool reliable = true);

/**
 * 发送媒体包
 *
 * @param instance YVRNET实例，通过 yvrnet_startup 获取
 * @param packet 媒体包
 * @param streamIndex 流ID
 * 
 * @return true 发送成功
 * @return false 发送失败
 */
YVRNET_API bool yvrnet_sendPacket(YvrnetInstance instance, const YvrnetMediaPacket *packet, int streamIndex = 0);

/**
 * 查询连接状态
 * 
 * @param instance YVRNET实例，通过 yvrnet_startup 获取
 * 
 * @return true 已连接
 * @return false 未连接
 */
YVRNET_API bool yvrnet_isConnected(YvrnetInstance instance);

#ifdef __cplusplus
} // extern "C"
#endif // __cplusplus

#endif // !YVRNET_YVRNET_C_WRAPPER_H