#ifndef YVRNET_YVRNET_TYPES_H
#define YVRNET_YVRNET_TYPES_H

#include <stdint.h>

#include "yvrnet_version.h"

typedef void* YvrnetInstance;
#define YVRNET_INVALID_INSTANCE NULL

/**
 * 错误码
 */
typedef enum YvrnetErrorCode
{
    YVRNET_EC_OK = 0,
    YVRNET_EC_NOT_STARTUP = 1,                          // 未初始化
    YVRNET_EC_DISCONNECTED = 2,                         // 未连接
    YVRNET_EC_DRIVER_INTERNAL_ERROR = 3,                // 驱动内部错误
    YVRNET_EC_INVALID_PARAM = 4,                        // 非法参数

    YVRNET_EC_STARTUP_VERSION_INCOMPATIBLE = 100,       // 版本不兼容
    YVRNET_EC_STARTUP_INVALID_DRIVER_TYPE = 101,        // 无效驱动类型
    YVRNET_EC_STARTUP_DRIVER_NOT_IMPLEMENT = 102,       // 驱动未实现
    YVRNET_EC_STARTUP_NOT_SUPPORT_MULTI_INSTANCE = 104, // 指定驱动不允许多实例
    YVRNET_EC_STARTUP_INIT_LOGGER_FAIL = 105,           // 初始化日志失败
    YVRNET_EC_STARTUP_BYTE_RTC_INIT_LICENSE_FAIL = 106, // 字节引擎 初始化License失败

    YVRNET_EC_START_PORT_OCCUPIED = 200,                // 端口被占用
    YVRNET_EC_START_BYTE_RTC_INVALID_LICENSE = 201,     // 字节引擎 License非法
    YVRNET_EC_START_CREATE_DEBUG_DIR_FAIL = 202,        // 创建Debug文件夹失败

} YvrnetErrorCode;

/**
 * 事件码
 */
typedef enum YvrnetEventID
{
    YVRNET_EI_NONE = 0,

    YVRNET_EI_REQUIRE_IDR = 1,                  // 请求关键帧
    YVRNET_EI_UPDATE_ENCODER_RATE = 2,          // 更新编码码率
    YVRNET_EI_START_ENCODER = 3,                // 启动编码器
    YVRNET_EI_STOP_ENCODER = 4,                 // 停止编码器
} YvrnetEventCode;

/**
 * 日志级别
 */
typedef enum YvrnetLogLevel
{
    YVRNET_LL_TRACE = 0,    // 全部日志
    YVRNET_LL_DEBUG = 1,    // Debug
    YVRNET_LL_INFO = 2,     // 普通信息
    YVRNET_LL_WARNING = 3,  // 警告
    YVRNET_LL_ERROR = 4,    // 错误
    YVRNET_LL_CRITICAL = 5, // 致命错误
    YVRNET_LL_OFF = 6       // 关闭日志
} YvrnetLogLevel;

/**
 * 网络驱动
 */
typedef enum YvrnetDriverType
{
    YVRNET_DT_MOCK = 0,     // 仅用于测试
    YVRNET_DT_YVRTC = 1,    // YVRTC
    YVRNET_DT_TCP = 2,      // TCP
    YVRNET_DT_UDP = 3,      // UDP（暂未实现）
    YVRNET_DT_BYTE_RTC = 4, // 字节火山引擎
    YVRNET_DT_STEAM = 5,    // Steam（暂未实现）
} YvrnetDriverType;

/**
 * 连接状态
 */
typedef enum YvrnetConnectionStatus
{
    YVRNET_CS_UNKNOWN = 0,  // 未知
    YVRNET_CS_CONNECTING,   // 连接中
    YVRNET_CS_CONNECTED,    // 已连接
    YVRNET_CS_DISCONNECTED, // 已断开
} YvrnetConnectionStatus;

/**
 * 媒体类型
 */
typedef enum YvrnetMediaType
{
    YVRNET_MT_UNKNOWN = 0,  // 未知
    YVRNET_MT_AUDIO = 1,    // 音频
    YVRNET_MT_VIDEO = 2,    // 视频
} YvrnetMediaType;

/**
 * 视频编解码类型
 */
typedef enum YvrnetVideoCodec
{
    YVRNET_VC_UNKNOWN = 0,  // 未知
    YVRNET_VC_H264 = 1,     // H264
    YVRNET_VC_H265 = 2,     // H265
} YvrnetVideoCodec;

/**
 * 音频编解码类型
 */
typedef enum YvrnetAudioCodec
{
    YVRNET_AC_UNKNOWN = 0,  // 未知
    YVRNET_AC_AAC = 1,      // AAC
    YVRNET_AC_OPUS = 2,
    YVRNET_AC_PCM = 3,
    YVRNET_AC_PCMA = 4,
    YVRNET_AC_PCMU = 5,
    YVRNET_AC_FLAC = 6,
} YvrnetAudioCodec;

/**
 * 编解码类型
 */
typedef union YvrnetAVCodec
{
    YvrnetVideoCodec video;
    YvrnetAudioCodec audio;
} YvrnetAVCodec;

/**
 * 视频帧类型
 */
typedef enum YvrnetVideoFrameType
{
    YVRNET_VFT_UNKNOWN = 0,
    YVRNET_VFT_I = 1,
    YVRNET_VFT_P = 2,
    YVRNET_VFT_B = 3,
} YvrnetVideoFrameType;

/**
 * 网络角色
 */
typedef enum YvrnetNetworkRole
{
    YVRNET_NR_CLIENT = 0,   // 客户端
    YVRNET_NR_SERVER = 1,   // 服务端
} YvrnetNetworkRole;

// 媒体编码包
typedef struct YvrnetMediaPacket
{
    YvrnetMediaType mediaType;  // 媒体类型
    YvrnetAVCodec codec;        // 编解码类型
    int64_t timestamp;          // 时间戳 us
    uint8_t *data;              // 媒体数据
    size_t size;                // 媒体数据大小 Byte
    uint8_t *extraData;         // 额外数据
    size_t extraDataSize;       // 额外数据大小 Byte
    uint32_t width;             // 视频宽度
    uint32_t height;            // 视频高度
} YvrnetMediaPacket;

// 网络统计
typedef struct YvrnetStatistics
{
    int rtt;                    // RTT
    float lossRate;             // 丢包率 [0, 1]
    int sendBitrate;            // 发送码率 kbps
    int receiveBitrate;         // 接收码率 kbps
    int e2e_delay;

    char reserved[508];
} YvrnetStatistics;

// 网络驱动 MOCK 配置
typedef struct YvrnetStartupConfigMock
{
    const char *filePath;
} YvrnetStartupConfigMock;

// 网络驱动 ByteRTC 配置
typedef struct YvrnetStartupConfigByteRTC
{
    void *context;              // Android context
    const char *licensePath;    // License存储路径
} YvrnetStartupConfigByteRTC;

// 网络驱动 YVRTC 配置
typedef struct YvrnetStartupConfigYVRTC
{
    YvrnetVideoCodec videoCodec;// 视频编码器
} YvrnetStartupConfigYVRTC;

// 启动配置
typedef struct YvrnetStartupConfig
{
    // SDK版本号，必须设置为 YVRNET_SDK_VERSION
    const char *version;

    // 网络驱动，建议值：YVRNET_DT_YVRTC
    YvrnetDriverType type;

    // 重试间隔，建议值：5
    int retryIntervalSecond;

    // 网络角色
    YvrnetNetworkRole role;

    // 日志文件夹
    const char *logDir;
    
    // 日志级别
    YvrnetLogLevel logLevel;

    // 调试模式
    bool debug;

    // 网络驱动配置, 取决于 type 类型
    union DriverConfig
    {
        YvrnetStartupConfigMock mock;
        YvrnetStartupConfigByteRTC byteRtc;
        YvrnetStartupConfigYVRTC yvrtc;

        char reserved[64];
    } config;

    // 如果添加新字段时，应在此处减去新字段的大小（注意结构体字节对齐）
    char reserved[256];
} YvrnetStartupConfig;

/**
 * 事件：请求I帧
 */
typedef struct YvrnetEvent_REQUIRE_IDR
{
    int streamIndex;
} YvrnetEvent_REQUIRE_IDR;

/**
 * 事件：更新编码器码率
 */
typedef struct YvrnetEvent_UPDATE_ENCODER_RATE
{
    int streamIndex;
    /**
     * 帧率，单位 fps
     */
    int fps;
    /**
     * 码率，单位 kbps
     */
    int bitrate_kbps;
} YvrnetEvent_UPDATE_ENCODER_RATE;

/**
 * 事件：启动编码器
 */
typedef struct YvrnetEvent_START_ENCODER
{
    int streamIndex;
} YvrnetEvent_START_ENCODER;

/**
 * 事件：停止编码器
 */
typedef struct YvrnetEvent_STOP_ENCODER
{
    int streamIndex;
} YvrnetEvent_STOP_ENCODER;

/**
 * 事件内容
 */
typedef struct YvrnetEvent
{
    YvrnetEventID eventId;  // 事件ID
    uint64_t timestampMs;   // 事件发生时间戳(Unix毫秒）
    union {
        YvrnetEvent_REQUIRE_IDR requireIdr;
        YvrnetEvent_UPDATE_ENCODER_RATE updateEncoderRate;
        YvrnetEvent_START_ENCODER startEncoder;
        YvrnetEvent_STOP_ENCODER stopEncoder;

        char reserved[256];
    } eventData;
} YvrnetEvent;

/**
 * 日志回调
 *
 * @param level 日志等级
 * @param log 日志内容
 * @param opaque 用户数据
 */
typedef void (*YvrnetLogCallback)(YvrnetLogLevel level, const char *log, void *opaque);

typedef void(*ReceiverTcp)(const uint8_t* data, int length);
typedef void(*ReceiverUdp)(const char* data, int length, const char* address);
typedef void(*ConnectGuard)(YvrnetConnectionStatus status);
#endif // !YVRNET_YVRNET_TYPES_H