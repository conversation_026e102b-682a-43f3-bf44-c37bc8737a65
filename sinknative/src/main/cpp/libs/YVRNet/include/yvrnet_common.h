#ifndef YVRNET_YVRNET_COMMON_H
#define YVRNET_YVRNET_COMMON_H

#include <string.h>

#include "yvrnet_types.h"

#ifdef _MSC_VER
    #ifdef yvrnet_EXPORTS
        #define YVRNET_API __declspec(dllexport)
    #else
        #define YVRNET_API
    #endif
#elif defined(__GNUC__)
    #ifdef yvrnet_EXPORTS
        #define YVRNET_API __attribute__((visibility("default")))
    #else
        #define YVRNET_API
    #endif
#else
    #define YVRNET_API
#endif

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

/**
 * 初始化启动配置
 * 
 * @param config 启动配置
 */
inline void yvrnet_initStartupConfig(YvrnetStartupConfig *config)
{
    if (!config)
        return;
    memset(config, 0, sizeof(*config));
    config->version = YVRNET_SDK_VERSION;
    config->type = YVRNET_DT_YVRTC;
    config->role = YVRNET_NR_CLIENT;
    config->logLevel = YVRNET_LL_INFO;
    config->debug = false;
    config->retryIntervalSecond = 5;
    config->config.yvrtc.videoCodec = YVRNET_VC_H265;
}

/**
 * 获取版本号
 * 
 * @return YVRNET SDK 版本号
 */
YVRNET_API const char *yvrnet_getVersion();

/**
 * 初始化网络
 * 
 * @param config 网络配置
 * @param error [out] 错误码
 * 
 * @return 网络实例，如果失败返回 YVRNET_INVALID_INSTANCE
 */
YVRNET_API YvrnetInstance yvrnet_startup(const YvrnetStartupConfig *config, YvrnetErrorCode *error);

/**
 * 销毁当前网络实例
 * 
 * @param instance 通过 yvrnet_startup 创建的实例
 */
YVRNET_API void yvrnet_shutdown(YvrnetInstance instance);

/**
 * 检测网络驱动是否支持多实例
 * 
 * @param driverType 驱动类型
 * 
 * @return true 指定网络驱动支持多实例
 * @return false 指定网络驱动不支持多实例
 */
YVRNET_API bool yvrnet_isSupportMultiInstance(YvrnetDriverType driverType);

/**
 * 设置日志回调
 *
 * @param callback 日志回调函数
 * @param opaque 用户数据，在 YvrnetLogCallback 中传递
 */
YVRNET_API void yvrnet_setLogCallback(YvrnetLogCallback callback, void *opaque);

/**
 * 输出错误文本
 * 
 * @param error 错误号
 * 
 * @return 错误号所对应的文本描述
 */
YVRNET_API const char *yvrnet_errorToString(YvrnetErrorCode errorCode);

/**
 * 输出事件
 *
 * @param event
 *
 * @return 错误号所对应的文本描述
 */
YVRNET_API const char *yvrnet_eventToString(YvrnetEventCode eventCode);

#ifdef __cplusplus
} // extern "C"
#endif // __cplusplus

#endif // !