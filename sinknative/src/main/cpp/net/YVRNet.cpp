#include "YVRNet.h"
#include "Common.h"


YVRNet::YVRNet() {
    mNet = nullptr;
}

YVRNet::~YVRNet() = default;

void YVRNet::start() {
    IConnector::start();
    YvrnetStartupConfig config;
    yvrnet_initStartupConfig(&config);
    config.role = getConnectionMode() == CONNECTION_MODE_CLIENT ? YVRNET_NR_CLIENT : YVRNET_NR_SERVER;
    config.type = YVRNET_DT_TCP;
//    config.config.yvrtc.videoCodec = YVRNET_VC_H264;
//    config.debug = true;
//    config.logLevel = YVRNET_LL_DEBUG;

    YvrnetErrorCode error;
    mNet = yvrnet_startup(&config, &error);
    if (error != YVRNET_EC_OK) {
        LOGE("Startup yvrnet fail: %s", yvrnet_errorToString(error));
        return;
    }
    yvrnet_getInstance(mNet)->addCallback(this);
    error = yvrnet_getInstance(mNet)->start(getIp().c_str(), std::stoi(getPort()));
    if (error != YVRNET_EC_OK) {
        LOGE("Start yvrnet fail: %s", yvrnet_errorToString(error));
        return;
    }
}

void YVRNet::stop() {
    IConnector::stop();
    if (mNet != nullptr) {
        yvrnet_getInstance(mNet)->stop();
        yvrnet_getInstance(mNet)->removeCallback(this);
        yvrnet_shutdown(mNet);
        mNet = nullptr;
    }
}

void YVRNet::send(const char *data, int size) {
    IConnector::send(data, size);
    if (mNet != nullptr)  {
        yvrnet_getInstance(mNet)->sendMessage(data, size);
    }
}

void YVRNet::send(const char *data, int size, int64_t timestamp, int width, int height) {
    IConnector::send(data, size, timestamp, width, height);
    if (mNet != nullptr) {
        YvrnetMediaPacket packet{};
        packet.data = (uint8_t *) data;
        packet.size = size;
        packet.width = width;
        packet.height = height;
        packet.timestamp = timestamp;
        packet.mediaType = YVRNET_MT_VIDEO;
        packet.codec.video = YVRNET_VC_H265;

        yvrnet_getInstance(mNet)->sendPacket(&packet);
    }
}

void YVRNet::onConnectStatus(YvrnetConnectionStatus status) {
    IYvrnetCallback::onConnectStatus(status);
    onConnectionChange(status == YVRNET_CS_CONNECTED);
}

void YVRNet::onReceiveMessage(const char *message, int length) {
    IYvrnetCallback::onReceiveMessage(message, length);
    onReceive(const_cast<char *>(message), length);
}

void YVRNet::onReceivePacket(const YvrnetMediaPacket *packet, int streamIndex) {
    IYvrnetCallback::onReceivePacket(packet, streamIndex);
    if (packet->mediaType == YVRNET_MT_VIDEO){
        onReceive(reinterpret_cast<const char *>(packet->data), packet->size, packet->codec.video, packet->width, packet->height, packet->extraData);
    } else{
        //YVRNET_MT_AUDIO
        onReceive(reinterpret_cast<const char *>(packet->data), packet->size, packet->codec.audio, packet->width, packet->height, packet->extraData);
    }
}
