#ifndef SCREENCASTSINK_YVRNET_H
#define SCREENCASTSINK_YVRNET_H


#include <yvrnet_cpp.h>
#include <memory>
#include "IConnector.h"

class YVRNet : public IConnector, public yvrnet::IYvrnetCallback {
public:
    YVRNet();
    ~YVRNet() override;
    void start() override;
    void stop() override;
    void send(const char *data, int size) override;
    void send(const char *data, int size, int64_t timestamp, int width, int height) override;

    void onConnectStatus(YvrnetConnectionStatus status) override;
    void onReceiveMessage(const char *message, int length) override;
    void onReceivePacket(const YvrnetMediaPacket *packet, int streamIndex) override;

private:
    YvrnetInstance mNet;
};


#endif //SCREENCASTSINK_YVRNET_H
