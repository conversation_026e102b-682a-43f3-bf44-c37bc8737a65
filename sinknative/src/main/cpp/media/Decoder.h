#ifndef SCREENCASTSINK_DECODER_H
#define SCREENCASTSINK_DECODER_H

#include "media/NdkMediaCodec.h"
#include "media/NdkMediaFormat.h"
#include <string>
#include <thread>

//#define ASYNC_DECODE

class Decoder {
public:
    class Callback {
    public:
        virtual ~Callback() = default;

        virtual void onOutputBufferAvailable(uint8_t *buffer, AMediaCodecBufferInfo *bufferInfo) = 0;

        virtual void onOutputFormatChanged(AMediaFormat *mediaFormat) = 0;
    };

public:
    Decoder();

    virtual ~Decoder();

    virtual void prepare();

    virtual void onPrepared();

    virtual void start();

    virtual void stop();

    virtual long dequeueInputBuffer();

    virtual long dequeueInputBuffer(int64_t timeoutUs);

    virtual bool inputBuffer(long index, uint8_t *buffer, uint32_t size, uint64_t timestamp);

    virtual long dequeueOutputBuffer(AMediaCodecBufferInfo *info);

    virtual uint8_t *outputBuffer(long index, size_t *out_size);

    virtual void releaseOutputBuffer(long index);

    virtual void releaseOutputBuffer(long index, bool b);

    virtual void decode();

    virtual void run();

    virtual void onDecoderStart();

    virtual void onDecoderStop();

    virtual void setCallback(Callback *callback);

    ANativeWindow *getWindow();

    void setWindow(ANativeWindow *window);

    void setBuffer(const char *name, const void *data, size_t size);

    void setId(int id);

    void setMimeType(std::string type);

protected:
    virtual void onInputAvailable(AMediaCodec *codec, int32_t index);

    virtual void onOutputAvailable(AMediaCodec *codec, int32_t index, AMediaCodecBufferInfo *bufferInfo);

    virtual void onFormatChanged(AMediaCodec *codec, AMediaFormat *format);

    virtual void onError(AMediaCodec *codec, media_status_t error, int32_t actionCode, const char *detail);

protected:
    AMediaCodec *mMediaCodec;
    AMediaFormat *mMediaFormat;
    ANativeWindow *mNativeWindow;
    Callback *mCallback;
    std::thread mThread;
    bool mRunning;
    int mId;
    std::string mMimeType;
    AMediaCodecOnAsyncNotifyCallback mMediaCodecOnAsyncNotifyCallback;
};

#endif //SCREENCASTSINK_DECODER_H
