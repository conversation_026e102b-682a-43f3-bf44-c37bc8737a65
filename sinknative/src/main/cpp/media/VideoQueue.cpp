#include "VideoQueue.h"
#include "Common.h"

VideoQueue::VideoQueue(bool lock) {
    mQueue = std::make_shared<VideoFrameQueue>();
    mQueue->front = 0;
    mQueue->rear = 0;
    mQueue->size = 0;
    mLock = lock;
}

VideoQueue::~VideoQueue() {
    mQueue.reset();
}

bool VideoQueue::push(uint8_t *buffer, uint32_t size, uint32_t type) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(buffer, size, type);
    }
    return pushInternal(buffer, size, type);
}

bool VideoQueue::push(uint8_t *buffer, uint32_t size, uint32_t type, int frameId) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(buffer, size, type, frameId);
    }
    return pushInternal(buffer, size, type, frameId);
}

bool VideoQueue::pushInternal(uint8_t *buffer, uint32_t size, uint32_t type) {
    if (mQueue->size >= VIDEO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(videoFrame->buffer, buffer, size);
    videoFrame->size = size;
    videoFrame->type = type;
    return true;
}

bool VideoQueue::pushInternal(uint8_t *buffer, uint32_t size, uint32_t type, int frameId) {
    if (mQueue->size >= VIDEO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(videoFrame->buffer, buffer, size);
    videoFrame->size = size;
    videoFrame->type = type;
    videoFrame->frameId = frameId;
    return true;
}

bool VideoQueue::push(VideoFrame *frame) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return pushInternal(frame);
    }
    return pushInternal(frame);
}

bool VideoQueue::pushInternal(VideoFrame *frame) {
    if (mQueue->size >= VIDEO_QUEUE_MAX_SIZE) {
        //LOGE("Queue is full");
        return false;
    }

    if (mQueue->rear >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->rear = 0;
    }

    mQueue->size += 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->rear++]);

    memcpy(videoFrame->buffer, frame->buffer, frame->size);
    videoFrame->size = frame->size;
    videoFrame->type = frame->type;
    return true;
}

bool VideoQueue::pop(VideoFrame *frame) {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return popInternal(frame);
    }
    return popInternal(frame);
}

bool VideoQueue::popInternal(VideoFrame *frame) {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return false;
    }

    if (mQueue->front >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->front++]);

    memcpy(frame->buffer, videoFrame->buffer, videoFrame->size);
    frame->size = videoFrame->size;
    frame->type = videoFrame->type;
    return true;
}

VideoFrame *VideoQueue::peek() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return peekInternal();
    }
    return peekInternal();
}

VideoFrame *VideoQueue::peekInternal() {
    if (mQueue->size <= 0) {
        return nullptr;
    }

    if (mQueue->front >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    VideoFrame *videoFrame = &(mQueue->frames[mQueue->front]);
    return videoFrame;
}

VideoFrame *VideoQueue::pop() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return popInternal();
    }
    return popInternal();
}

VideoFrame *VideoQueue::popInternal() {
    if (mQueue->size <= 0) {
        //LOGE("Queue is empty");
        return nullptr;
    }

    if (mQueue->front >= VIDEO_QUEUE_MAX_SIZE) {
        mQueue->front = 0;
    }

    mQueue->size -= 1;
    VideoFrame *videoFrame = &(mQueue->frames[mQueue->front++]);
    return videoFrame;
}

bool VideoQueue::isEmpty() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return isEmptyInternal();
    }
    return isEmptyInternal();
}

bool VideoQueue::isEmptyInternal() {
    return mQueue->size <= 0;
}

bool VideoQueue::isFull() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return isFullInternal();
    }
    return isFullInternal();
}

bool VideoQueue::isFullInternal() {
    return mQueue->size >= VIDEO_QUEUE_MAX_SIZE;
}

long VideoQueue::length() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        return lengthInternal();
    }
    return lengthInternal();
}

long VideoQueue::lengthInternal() {
    return mQueue->size;
}

void VideoQueue::clear() {
    if (mLock) {
        std::lock_guard<std::mutex> lock(mMutex);
        mQueue->size = 0;
        mQueue->front = 0;
        mQueue->rear = 0;
        return;
    }

    mQueue->size = 0;
    mQueue->front = 0;
    mQueue->rear = 0;
}
