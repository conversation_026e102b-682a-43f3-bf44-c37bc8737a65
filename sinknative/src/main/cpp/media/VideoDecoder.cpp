#include "VideoDecoder.h"
#include "Common.h"
#include <sys/resource.h>

VideoDecoder::VideoDecoder() {
    mWidth = 0;
    mHeight = 0;
}

VideoDecoder::~VideoDecoder() = default;

void VideoDecoder::prepare() {
    Decoder::prepare();

    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_WIDTH, mWidth);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_HEIGHT, mHeight);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_PRIORITY, 0);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_OPERATING_RATE, 32767);
    AMediaFormat_setInt32(mMediaFormat, "vendor.qti-ext-dec-low-latency.enable", 1);
}

void VideoDecoder::onPrepared() {
    Decoder::onPrepared();
}

void VideoDecoder::onDecoderStart() {
    Decoder::onDecoderStart();
    LOGI("[%d]video decoder on start", mId);

    std::string threadName = "video-decoder" + std::to_string(mId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for video decoder thread:%d rc:%d", mId, self, rc);
}

void VideoDecoder::onDecoderStop() {
    Decoder::onDecoderStop();
    LOGI("[%d]video decoder on stop", mId);
}

void VideoDecoder::setSize(int width, int height) {
    mWidth = width;
    mHeight = height;
}
