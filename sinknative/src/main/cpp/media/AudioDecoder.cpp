#include "AudioDecoder.h"
#include "Common.h"
#include <sys/resource.h>

AudioDecoder::AudioDecoder() {
    mChannelCount = 0;
    mSampleRate  = 0;
}

AudioDecoder::~AudioDecoder() = default;

void AudioDecoder::prepare() {
    Decoder::prepare();

    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_CHANNEL_COUNT, mChannelCount);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_SAMPLE_RATE, mSampleRate);
    AMediaFormat_setInt32(mMediaFormat, AMEDIAFORMAT_KEY_OPERATING_RATE, 32767);
    AMediaFormat_setInt32(mMediaFormat, "vendor.qti-ext-enc-low-latency.enable", 1);
    unsigned char csd_0[4] = {0xf8, 0xe6, 0x40, 0x00};
    AMediaFormat_setBuffer(mMediaFormat, AMEDIAFORMAT_KEY_CSD_0, csd_0, sizeof(csd_0));
}

void AudioDecoder::onPrepared() {
    Decoder::onPrepared();
}

void AudioDecoder::onDecoderStart() {
    Decoder::onDecoderStart();
    LOGI("[%d]audio decoder on start", mId);

    std::string threadName = "audio-decoder" + std::to_string(mId);
    pthread_setname_np(pthread_self(), threadName.c_str());

    pid_t self = gettid();
    int rc = setpriority(PRIO_PROCESS, self, -16);
    LOGI("[%d]set priority for audio decoder thread:%d rc:%d", mId, self, rc);
}

void AudioDecoder::onDecoderStop() {
    Decoder::onDecoderStop();
    LOGI("[%d]audio decoder on stop", mId);
}

void AudioDecoder::setChannelCount(int channelCount) {
    mChannelCount = channelCount;
}

void AudioDecoder::setSampleRate(int sampleRate) {
    mSampleRate = sampleRate;
}
