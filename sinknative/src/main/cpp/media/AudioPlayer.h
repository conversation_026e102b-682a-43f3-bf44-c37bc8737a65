#ifndef SCREENCASTSINK_AUDIOPLAYER_H
#define SCREENCASTSINK_AUDIOPLAYER_H


#include "Player.h"
#include "utils/CircularBlockingQueue.hpp"
#include "net/IConnector.h"
#include "AudioDecoder.h"
#include "Common.h"
#include "AudioQueue.h"
#include <aaudio/AAudio.h>
#include <queue>
#include <fstream>

//#define DEBUG_AUDIO

class AudioPlayer : public Player, public AudioDecoder {
public:
    AudioPlayer();

    ~AudioPlayer() override;

    void start() override;

    void stop() override;

    void configure(Player::Config &config) override;

    void setFrameDropThreshold(int threshold);

protected:
    void onReceived(const char *data, int size) override;

    void onReceived(const char *data, int size, int mediaType, int width, int height, void *pStatus) override;

    void onConnectionChanged(bool connected) override;

    void onStart() override;

    void onStop() override;

    void render() override;

    void decode() override;

    static void printAudioStreamInfo(const AAudioStream *stream);

    static void onAudioStreamError(AAudioStream *stream, void *userData, aaudio_result_t error);

private:
    typedef struct {
        uint8_t buffer[2048];
        long size;
        long timestamp;
        int frameId;
    } AudioData;

    AudioQueue mQueue;
    CircularBlockingQueue<std::shared_ptr<AudioData>, 20> mRenderQueue;
    AAudioStreamBuilder *mBuilder;
    AAudioStream *mAAudioStream;
    std::mutex mMutex;

#ifdef DEBUG_AUDIO
    std::ofstream mStream;
#endif
};

#endif //SCREENCASTSINK_AUDIOPLAYER_H
