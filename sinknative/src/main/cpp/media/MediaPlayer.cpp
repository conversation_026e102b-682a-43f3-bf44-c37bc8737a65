#include "MediaPlayer.h"

MediaPlayer::MediaPlayer() {
    mVideoPlayer = nullptr;
    mAudioPlayer = nullptr;
    mVideoPlayerCallback = nullptr;
    mAudioPlayerCallback = nullptr;
    mWindow = nullptr;
    mCallback = nullptr;
}

MediaPlayer::~MediaPlayer() = default;

void MediaPlayer::configure(Player::Config &config) {
    mConfig = config;
}

void MediaPlayer::setSurfaceTexture(ASurfaceTexture *surfaceTexture) {
    mSurfaceTexture = surfaceTexture;
}

void MediaPlayer::setWindow(ANativeWindow *window) {
    mWindow = window;
}

void MediaPlayer::setCallback(MediaPlayer::Callback *callback) {
    mCallback = callback;
}

void MediaPlayer::start() {
    mVideoPlayer = std::make_shared<VideoPlayer>();
    mAudioPlayer = std::make_shared<AudioPlayer>();

    mVideoPlayerCallback = new VideoPlayerCallback(this);
    mAudioPlayerCallback = new AudioPlayerCallback(this);

    mVideoPlayer->Player::setCallback(mVideoPlayerCallback);
    mAudioPlayer->Player::setCallback(mAudioPlayerCallback);

    mVideoPlayer->setSurfaceTexture(mSurfaceTexture);
    mVideoPlayer->setWindow(mWindow);
    mVideoPlayer->configure(mConfig);
    mAudioPlayer->configure(mConfig);

    mVideoPlayer->start();
    mAudioPlayer->start();
}

void MediaPlayer::stop() {
    mVideoPlayer->stop();
    mAudioPlayer->stop();

    mVideoPlayer->Player::setCallback(nullptr);
    mAudioPlayer->Player::setCallback(nullptr);

    delete mVideoPlayerCallback;
    delete mAudioPlayerCallback;
}

bool MediaPlayer::isVideoConnected() {
    return mVideoPlayer != nullptr && mVideoPlayer->isConnected();
}

bool MediaPlayer::isAudioConnected() {
    return mAudioPlayer != nullptr && mAudioPlayer->isConnected();
}

void MediaPlayer::setVideoFrameDropThreshold(int threshold) {
    if (mVideoPlayer != nullptr) mVideoPlayer->setFrameDropThreshold(threshold);
}

void MediaPlayer::setAudioFrameDropThreshold(int threshold) {
    if (mAudioPlayer != nullptr) mAudioPlayer->setFrameDropThreshold(threshold);
}

void MediaPlayer::setFocused(bool focused) {
    if (mVideoPlayer != nullptr) mVideoPlayer->setFocused(focused);
    if (mAudioPlayer != nullptr) mAudioPlayer->setFocused(focused);
}

void MediaPlayer::onFrameAvailable() {
    if (mVideoPlayer != nullptr) mVideoPlayer->onFrameAvailable();
}

void MediaPlayer::clearAvailable() {
    if (mVideoPlayer != nullptr) mVideoPlayer->clearAvailable();
}

void MediaPlayer::VideoPlayerCallback::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onVideoOutputFormatChanged(mediaFormat);
    }
}

void MediaPlayer::VideoPlayerCallback::onConnectionChanged(bool connected) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onVideoConnectionChanged(connected);
    }
}

void MediaPlayer::AudioPlayerCallback::onOutputFormatChanged(AMediaFormat *mediaFormat) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onAudioOutputFormatChanged(mediaFormat);
    }
}

void MediaPlayer::AudioPlayerCallback::onConnectionChanged(bool connected) {
    if (mPlayer->mCallback != nullptr) {
        mPlayer->mCallback->onAudioConnectionChanged(connected);
    }
}
