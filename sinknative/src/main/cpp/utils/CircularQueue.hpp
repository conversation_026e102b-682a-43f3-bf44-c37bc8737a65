#ifndef CIRCULARQUEUE_H
#define CIRCULARQUEUE_H

#include <iostream>
#include <array>
#include <mutex>
#include <stdexcept>

template<typename T, std::size_t N>
class CircularQueue {
public:
    CircularQueue() : head(0), tail(0), count(0) {
        if (N == 0) {
            throw std::invalid_argument("Queue size must be greater than 0");
        }
    }

    bool tryPush(const T &item) {
        std::unique_lock<std::mutex> lock(mtx);
        if (count >= N) {
            return false; // Queue is full
        }

        buffer[tail] = item;
        tail = (tail + 1) % N;
        ++count;

        return true;
    }

    bool tryPop(T &item) {
        std::unique_lock<std::mutex> lock(mtx);
        if (count <= 0) {
            return false; // Queue is empty
        }

        item = buffer[head];
        head = (head + 1) % N;
        --count;

        return true;
    }

    std::size_t size() const {
        std::unique_lock<std::mutex> lock(mtx);
        return count;
    }

    bool empty() const {
        return size() == 0;
    }

    bool full() const {
        return size() == N;
    }

    void clear() {
        std::unique_lock<std::mutex> lock(mtx);
        head = 0;
        tail = 0;
        count = 0;
    }

private:
    std::array<T, N> buffer;
    std::size_t head;
    std::size_t tail;
    std::size_t count;
    mutable std::mutex mtx;
};


#endif //CIRCULARQUEUE_H
