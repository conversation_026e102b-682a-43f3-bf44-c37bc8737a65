package com.pfdm.screencastsink;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class ScreencastReceiver extends BroadcastReceiver {
    private static final String TAG = "ScreencastSinkReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.i(TAG, "on receive action = " + action);
        if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
            context.startService(new Intent(context, ScreencastService.class));
        }
    }
}
